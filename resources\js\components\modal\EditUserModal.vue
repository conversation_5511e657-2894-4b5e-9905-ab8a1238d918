<template>
  <Transition
    enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition ease-in duration-150"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="modelValue"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/75"
      aria-modal="true"
      role="dialog"
    >
      <Transition
        enter-active-class="transition ease-out duration-300"
        enter-from-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        enter-to-class="opacity-100 translate-y-0 sm:scale-100"
        leave-active-class="transition ease-in duration-200"
        leave-from-class="opacity-100 translate-y-0 sm:scale-100"
        leave-to-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
      >
        <div
          v-if="modelValue"
          class="relative w-full max-w-2xl mx-4 bg-white rounded-lg shadow-xl"
        >
          <!-- Header -->
          <div class="flex items-center justify-between px-6 py-2 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">
              Chỉnh Sửa Người Dùng
            </h3>
          </div>

          <!-- Body -->
          <div class="px-6 py-4 overflow-y-auto" style="max-height: 75vh;">
            <!-- Trạng thái Loading -->
            <div v-if="isLoading" class="flex items-center justify-center py-10">
              <svg class="w-8 h-8 mr-3 -ml-1 text-blue-500 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span class="text-gray-600">Đang tải dữ liệu...</span>
            </div>

            <!-- Trạng thái Lỗi -->
            <div v-else-if="error" class="px-4 py-3 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
              <strong class="font-bold">Lỗi!</strong>
              <span class="block sm:inline"> {{ error }}</span>
            </div>

            <!-- Hiển thị dữ liệu -->
            <div v-else-if="userData" class="space-y-4">
              <div class="grid grid-cols-1 gap-4">
                <!-- Tên người dùng -->
                <div class="space-y-1">
                  <label for="name" class="block text-sm font-medium text-gray-700">Tên người dùng *</label>
                  <input type="text" id="name" v-model="userData.name"
                    class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Nhập tên người dùng" />
                </div>

                <!-- Email -->
                <div class="space-y-1">
                  <label for="email" class="block text-sm font-medium text-gray-700">Email *</label>
                  <input type="email" id="email" v-model="userData.email"
                    class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Nhập địa chỉ email" />
                </div>

                <!-- Mật khẩu mới (tùy chọn) -->
                <div class="space-y-1">
                  <label for="password" class="block text-sm font-medium text-gray-700">Mật khẩu mới (để trống nếu không đổi)</label>
                  <input type="password" id="password" v-model="formData.password"
                    class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Nhập mật khẩu mới" />
                </div>

                <!-- Xác nhận mật khẩu mới -->
                <div class="space-y-1" v-if="formData.password">
                  <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Xác nhận mật khẩu mới *</label>
                  <input type="password" id="password_confirmation" v-model="formData.password_confirmation"
                    class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Nhập lại mật khẩu mới" />
                </div>

                <!-- Vai trò -->
                <div class="space-y-1">
                  <label for="roles" class="block text-sm font-medium text-gray-700">Vai trò</label>
                  <select id="roles" v-model="selectedRole" :disabled="isLoadingRoles"
                    class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">Chọn vai trò</option>
                    <option v-for="role in roleOptions" :key="role.name" :value="role.name">
                      {{ role.vi_name }}
                    </option>
                  </select>
                  <div v-if="roleError" class="mt-1 text-sm text-red-600">
                    {{ roleError }}
                  </div>
                </div>

                <!-- Trạng thái -->
                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">Trạng thái hiện tại</label>
                  <div class="p-2 bg-gray-50 rounded-md">
                    <span :class="getStatusClass(userData.status)">
                      {{ userData.status || 'Đang hoạt động' }}
                    </span>
                  </div>
                </div>

                <!-- Ngày tạo -->
                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">Ngày tạo</label>
                  <div class="p-2 bg-gray-50 rounded-md text-sm text-gray-600">
                    {{ userData.created_at ? new Date(userData.created_at).toLocaleDateString('vi-VN') : 'Không có thông tin' }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="flex items-center justify-end px-6 py-2 space-x-3 bg-gray-50 rounded-b-lg">
            <button
              @click="closeModal"
              type="button"
              :disabled="isSubmitting"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Đóng
            </button>
            <button
              @click="submitForm"
              type="button"
              :disabled="isSubmitting || isLoading || !!error"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isSubmitting" class="flex items-center">
                <svg class="w-4 h-4 mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Đang cập nhật...
              </span>
              <span v-else>Cập nhật</span>
            </button>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed } from 'vue';
import type { PropType } from 'vue';
import axios from 'axios';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  userId: {
    type: [Number, String, null] as PropType<number | string | null>,
    required: true,
  },
});

// Emits
const emit = defineEmits(['update:modelValue', 'user-updated']);

// State
const userData = ref<Record<string, any> | null>(null);
const isLoading = ref(false);
const isSubmitting = ref(false);
const error = ref<string | null>(null);

// Form data for password changes
const formData = ref({
  password: '',
  password_confirmation: ''
});

// Role options
interface RoleOption {
  name: string;
  vi_name: string;
}
const roleOptions = ref<RoleOption[]>([]);
const isLoadingRoles = ref(false);
const roleError = ref<string | null>(null);
const selectedRole = ref('');

// Constants for API timeout and error messages
const API_TIMEOUT = 30000; // 30 seconds
const ERROR_MESSAGES = {
  TIMEOUT: 'Thao tác đã hết thời gian chờ. Vui lòng thử lại.',
  NETWORK: 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.',
  UNKNOWN: 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.',
};

// Reset state functions
const resetModalState = () => {
  isLoading.value = false;
  isSubmitting.value = false;
  error.value = null;
  userData.value = null;
  formData.value.password = '';
  formData.value.password_confirmation = '';
  selectedRole.value = '';
};

// Close Modal Logic
const closeModal = () => {
  resetModalState();
  emit('update:modelValue', false);
};

// Get status class for styling
const getStatusClass = (status: string) => {
  const statusValue = status || 'Đang hoạt động';
  let colorClass = 'bg-gray-100 text-gray-800';
  if (statusValue === 'Đang hoạt động') colorClass = 'bg-green-100 text-green-800';
  else if (statusValue === 'Ngừng hoạt động') colorClass = 'bg-yellow-100 text-yellow-800';
  else if (statusValue === 'Bị khóa') colorClass = 'bg-red-100 text-red-800';
  return `px-2 py-1 text-xs font-medium rounded-full ${colorClass}`;
};

const loadRoleOptions = async () => {
  try {
    isLoadingRoles.value = true;
    roleError.value = null;
    
    // Using the roles from the seeder data
    roleOptions.value = [
      { name: 'admin', vi_name: 'Quản trị viên' },
      { name: 'specialist_data', vi_name: 'Chuyên viên Dữ liệu' },
      { name: 'specialist_report', vi_name: 'Chuyên viên Báo cáo' },
      { name: 'specialist_user', vi_name: 'Chuyên viên Người dùng' },
      { name: 'guest', vi_name: 'Khách' }
    ];
  } catch (error: any) {
    console.error('Error loading role options:', error);
    roleError.value = error.message || 'Có lỗi xảy ra khi tải danh sách vai trò.';
  } finally {
    isLoadingRoles.value = false;
  }
};

// Fetch User Data Logic
const fetchUserDetails = async () => {
  if (!props.userId) {
    error.value = "Không có ID người dùng được cung cấp.";
    return;
  }

  isLoading.value = true;
  error.value = null;
  userData.value = null;

  try {
    const response = await axios.get(`/api/users/${props.userId}`);
    userData.value = response.data.data;
    
    // Set the current role if available
    if (userData.value.role?.name) {
      selectedRole.value = userData.value.role.name;
    }
  } catch (err: any) {
    console.error('Failed to fetch user details:', err);
    error.value = 'Không thể tải dữ liệu người dùng. Vui lòng thử lại.';
  } finally {
    isLoading.value = false;
  }
};

// Submit form
const submitForm = async () => {
  if (!props.userId || !userData.value) {
    error.value = "Không có dữ liệu người dùng để cập nhật.";
    return;
  }

  isSubmitting.value = true;
  error.value = null;

  try {
    // Validate required fields
    if (!userData.value.name) {
      error.value = 'Vui lòng nhập tên người dùng';
      return;
    }

    if (!userData.value.email) {
      error.value = 'Vui lòng nhập email';
      return;
    }

    // Validate password confirmation if password is provided
    if (formData.value.password && formData.value.password !== formData.value.password_confirmation) {
      error.value = 'Mật khẩu xác nhận không khớp';
      return;
    }

    // Prepare data for API
    const apiData: any = {
      name: userData.value.name,
      email: userData.value.email,
      roles: selectedRole.value ? [selectedRole.value] : []
    };

    // Only include password if it's provided
    if (formData.value.password) {
      apiData.password = formData.value.password;
      apiData.password_confirmation = formData.value.password_confirmation;
    }

    console.log('Sending API data:', apiData);

    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('TIMEOUT')), API_TIMEOUT);
    });

    // Create the API request promise
    const apiPromise = axios.put(`/api/users/${props.userId}`, apiData);

    // Race between timeout and API request
    const response = await Promise.race([apiPromise, timeoutPromise]) as any;

    // Validate response success
    if (!response?.data?.success && !response?.status?.toString().startsWith('2')) {
      throw new Error(response?.data?.message || 'Lỗi không xác định từ server');
    }

    // If we reach here, update was successful
    try {
      // Emit the update event before closing
      emit('user-updated', props.userId);
      
      // Add a small delay for visual feedback
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Close the modal and reset state
      closeModal();

      // Show success message
      alert('Cập nhật người dùng thành công!');
    } catch (emitError) {
      console.error('Error during post-update handling:', emitError);
      // Still close the modal since the update was successful
      closeModal();
    }

  } catch (err: any) {
    console.error('Lỗi khi cập nhật người dùng:', err);
    
    // Handle different types of errors
    if (err.message === 'TIMEOUT') {
      error.value = ERROR_MESSAGES.TIMEOUT;
    } else if (err.isAxiosError && !err.response) {
      // Network error
      error.value = ERROR_MESSAGES.NETWORK;
    } else {
      // Server error or other errors
      error.value = err.response?.data?.message 
        || err.message 
        || ERROR_MESSAGES.UNKNOWN;
    }
    
    // Keep the modal open for error state
    isSubmitting.value = false;
  }
};

// Watch for modal open
watch(() => props.modelValue, async (newValue) => {
  if (newValue) {
    document.body.style.overflow = 'hidden';
    await loadRoleOptions();
    await fetchUserDetails();
  } else {
    document.body.style.overflow = '';
    resetModalState();
  }
});

// Handle Escape key for closing modal
const handleKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && props.modelValue) {
    closeModal();
  }
};

onMounted(() => {
  window.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown);
  // Ensure body scroll is restored if component is unmounted while modal is open
  document.body.style.overflow = '';
});
</script>
