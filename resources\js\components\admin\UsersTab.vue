<script setup lang="ts">
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Icon from '@/components/Icon.vue'
import DataTable from '@/components/DataTable.vue'

const users = ref<any[]>([])
const loading = ref(false)
const error = ref('')

onMounted(async () => {
  loading.value = true
  try {
    const res = await axios.get('/api/users')
    users.value = res.data.data || res.data // Tùy API trả về
  } catch (e: any) {
    error.value = 'Không thể tải danh sách người dùng.'
  } finally {
    loading.value = false
  }
})

const ActionButtons = (params: any) => {
  const eDiv = document.createElement('div');
  eDiv.className = 'flex gap-1';

  // Edit button
  const editBtn = document.createElement('button');
  editBtn.className = 'inline-flex items-center justify-center w-6 h-6 text-gray-500 hover:text-green-600';
  editBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/></svg>`;
  //editBtn.onclick = () => openViewModal(params.data.id);
  eDiv.appendChild(editBtn);

  // Delete button
  const deleteBtn = document.createElement('button');
  deleteBtn.className = 'inline-flex items-center justify-center w-6 h-6 text-gray-500 hover:text-red-600';
  deleteBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>`;
 // deleteBtn.onclick = () => openDeleteModal(params.data.id);
  eDiv.appendChild(deleteBtn);

  return eDiv;
};

const columnDefs = [
  {
    headerName: 'Hành động',
    cellRenderer: ActionButtons,
    width: 110,
    pinned: 'left',
    suppressMenu: true,
    sortable: false,
    filter: false,
    resizable: false,
  },
  {
    headerName: 'Tên người dùng',
    field: 'name',
    minWidth: 180,
    cellRenderer: (params: any) => {
      const user = params.data
      return `<div class=\"flex items-center gap-3\">

        
        <div><div class=\"font-medium\">${user.name || ''}</div><div class=\"text-sm text-muted-foreground\">${user.username || ''}</div></div>
      </div>`
    },
    sortable: true,
    filter: true
  },
  {
    headerName: 'Email',
    field: 'email',
    minWidth: 160,
    sortable: true,
    filter: true
  },
  {
    headerName: 'Vai trò',
    field: 'role',
    minWidth: 120,
    valueGetter: (params: any) => params.data.role?.vi_name || params.data.role?.name || '',
    sortable: true,
    filter: true
  },
/*   {
    headerName: 'Đơn vị',
    field: 'unit',
    minWidth: 120,
    sortable: true,
    filter: true
  }, */
  {
    headerName: 'Trạng thái',
    field: 'status',
    minWidth: 120,
    cellRenderer: (params: any) => {
      const status = params.value || 'Đang hoạt động'
      let color = 'bg-gray-100 text-gray-800'
      if (status === 'Đang hoạt động') color = 'bg-green-100 text-green-800'
      else if (status === 'Ngừng hoạt động') color = 'bg-yellow-100 text-yellow-800'
      else if (status === 'Bị khóa') color = 'bg-red-100 text-red-800'
      return `<span class=\"px-2 py-1 text-xs font-medium rounded-full ${color}\">${status}</span>`
    },
    sortable: true,
    filter: true
  },
  {
    headerName: 'Ngày tạo',
    field: 'created_at',
    minWidth: 120,
    valueGetter: (params: any) => params.data.created_at ? (new Date(params.data.created_at).toLocaleDateString('vi-VN')) : '',
    sortable: true,
    filter: true
  },
/*   {
    headerName: '',
    field: 'actions',
    minWidth: 60,
    cellRenderer: () => {
      return `<button class=\"p-1\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\"><circle cx=\"12\" cy=\"12\" r=\"2\"/><circle cx=\"19\" cy=\"12\" r=\"2\"/><circle cx=\"5\" cy=\"12\" r=\"2\"/></svg></button>`
    },
    suppressMenu: true,
    suppressSorting: true,
    width: 60
  } */
]
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold">
        Quản Lý Người Dùng
      </h2>
      <Button>
        <Icon name="Plus" class="h-4 w-4 mr-2" />
        Thêm người dùng
      </Button>
    </div>
    <div class="flex gap-4 mb-4">
      <Input placeholder="Tìm kiếm người dùng..." class="max-w-xs" />
      <select class="border rounded px-2 py-1">
        <option>Tất cả vai trò</option>
      </select>
      <select class="border rounded px-2 py-1">
        <option>Tất cả trạng thái</option>
      </select>
    </div>
    <div v-if="loading" class="p-4 text-center text-gray-500">Đang tải...</div>
    <div v-else-if="error" class="p-4 text-center text-red-500">{{ error }}</div>
    <div v-else>
      <DataTable
        :columnDefs="columnDefs"
        :rowData="users"
        :height="'100%'"
        :maxHeight="'60vh'"
        theme="ag-theme-alpine"
        locale="vi-VN"
      />
    </div>
  </div>
</template>
